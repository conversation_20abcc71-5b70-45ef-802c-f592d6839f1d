import numpy as np
import os
import matplotlib.pyplot as plt

from ripple.field import Trans<PERSON><PERSON>ield
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens, SingleSurface
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.geometry import rotate_vector
from ripple.utils.optics import cal_exit_angle
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_dir


coupling_distance = 300 + 600
coupling_angle_deg = -8
c_wl = 1.55
global_x_offset = 40

monitor_xy = FieldMonitor(monitor_type='xy', position=0.1, saving_path=None)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_power_per_z=True,
                          record_beam_radius_per_z=True)

source_field = TransverseField(distribution=GaussianDistribution(x0=global_x_offset, y0=0, w0x=2.87/2, w0y=3.28/2),
                               z0=0, refractive_index=1.53, wavelength=c_wl)

# Coupling efficiency: 0.9718570168683536
lens1 = SingleSurface(
            surface_type='ConoidalAsphericSurface',
            surface_parameters={'x0': -7.056561389823452, 'y0': 0, 'z0': 87.02549925008005,
                                'rho_x': -0.035083855643193516, 'kappa_x': -0.6309195892700106,
                                'rho_y': -0.03510415321921615, 'kappa_y': -0.46639070280961215},
            max_radius=125/2, material_side=-1, refractive_index=1.53, priority=2)
lens1.translate(tx=global_x_offset, ty=0, tz=0)

lens2_len = coupling_distance - 633.0113102999824 #260
print(f"{lens2_len = }")
lens2 = SingleSurface(
            surface_type='ConoidalAsphericSurface',
            surface_parameters={'x0': 20.761047766002555, 'y0': 0, 'z0': coupling_distance - lens2_len,
                                'rho_x': 0.011991450911873277, 'kappa_x': -0.4693450430957874,
                                'rho_y': 0.012720502681411405, 'kappa_y': -0.45689370962889575},
            max_radius=125/2, material_side=1, refractive_index=1.53, priority=2)

target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=10.4/2, w0y=10.4/2),
                               z0=coupling_distance, refractive_index=1.53, wavelength=c_wl)

# lens2_x_offset = (coupling_distance - lens1._surface_parameters['z0'] - lens2_len) * np.sin(np.deg2rad(coupling_angle_deg))
lens2_x_offset = (coupling_distance - 82.42423872145699 - 260) * np.sin(np.deg2rad(coupling_angle_deg)) # has to set to constance otherwise the result changes depending on the optimization
lens2.translate(tx=lens2_x_offset + global_x_offset,
                ty=0, tz=0, attach_to=target_field, attach_after_translation=False)

scene = CouplingScene(input_field=source_field, target_field=target_field,
                      background_material_index=1.,
                      optical_structures=[lens1, lens2],
                      sim_size_xy=[250, 250],
                      # sim_z_end=target_center_z + 10,
                      wavelength_sampling_xy=4,
                      material_wavelength_sampling_z=4,
                      background_wavelength_sampling_z=2,
                      # solver='wpm',
                      solver='bvwpm', solver_pars={'polarization': 'x'},
                      monitors=[monitor_xy, monitor_xz],
                      boundary_condition='PML', boundary_parameters={"boundary_thickness": [5]*2})

if __name__ == "__main__":
    current_dir = os.path.abspath(os.path.dirname(__file__))
    save_dir = os.path.join(current_dir, '1550_8deg')
    # save_dir=None
    if False:
        # scene.preview(mode='xz', position=0)
        scene.preview(mode='yz', position=lens2_x_offset + global_x_offset)
        plt.show()

    if False:
        opt_pars = {
            lens1.name: {
                'Surface': {
                    'surface': ['x0', 'z0', 'kappa_x', 'kappa_y', 'rho_x', 'rho_y'],
                },
                # 'Translation': ['x']
            },
            lens2.name: {
                'Surface': {
                    'surface': ['x0', 'z0', 'kappa_x', 'kappa_y', 'rho_x', 'rho_y'],
                },
                # 'Translation': ['x']
            }
        }

        run_sim(scene, optimize_structure=False, sim_result_dir=save_dir, opt_log_dir=save_dir,
                opt_pars=opt_pars, cal_coupling=True, show_plot=True, direction='reflected')

    if False:
        plot_monitor_from_dir(dir_path=r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\02_Fiber_1chEDWA4_01_Fiber', scene=scene)
        plt.show()

    if True:
        monitor_path = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\03_Fiber_D211_Fiber\04_1550nm_8deg_SQS\0_monitor_xy_2025-08-05_16-32-57.pkl'
        monitor = FieldMonitor.load(path=monitor_path)

        ce = monitor.cal_overlap_integral(input_field=source_field, field_type='E', operation='abs', direction='reflected', debug=True)
        print(f"{ce = }")   # 0.0012126304227409186 (4, 4, 2)

