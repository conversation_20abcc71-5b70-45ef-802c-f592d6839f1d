from ripple.utils.export_mesh import export_model
from wave_propagation_method.optical_elements2.basic_elements import Lens


save_path = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\04_D143_LGT_debug\EDWA\12_1550_gaussian_25um.obj'

abs_x_offset = 0    # only used for calculating the semidiameter
semi_diameter = 100
lens1_base = Lens('plane', [0],
                  'conoidal_xy_base', {'conoidal_coeff': {
                                                          'z0': 77.67146751687815,
                                'rho_x': -0.03903201971691426, 'kappa_x': -0.48021187242204966,
                                'rho_y': -0.0391351353256688, 'kappa_y': -0.46332946028635374
                                                          # 'surf_offset_xy': (abs_x_offset, 0)
                                                          }},
                  semidiameter=(semi_diameter + abs_x_offset) * 1.2,
                  material_index=1.53, element_name='L1')
export_model(yilin_lens_def=lens1_base, model_path=save_path, z_lim=[31, 79], build_ipt_model=True, preview=False)
