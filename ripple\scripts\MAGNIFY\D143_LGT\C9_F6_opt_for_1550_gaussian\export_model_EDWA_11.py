from ripple.utils.export_mesh import export_model
from wave_propagation_method.optical_elements2.basic_elements import Lens


save_path = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\04_D143_LGT_debug\dummy\11_1550_gaussian_30um.obj'

abs_x_offset = 0    # only used for calculating the semidiameter
semi_diameter = 100
lens1_base = Lens('plane', [0],
                  'conoidal_xy_base', {'conoidal_coeff': {
                                                          'z0':  83.6846552549795,
                                'rho_x': -0.035500467068221395, 'kappa_x': -0.47506092870575023,
                                'rho_y': -0.03558087746288397, 'kappa_y': -0.4573185525020685
                                                          # 'surf_offset_xy': (abs_x_offset, 0)
                                                          }},
                  semidiameter=(semi_diameter + abs_x_offset) * 1.2,
                  material_index=1.53, element_name='L1')
export_model(yilin_lens_def=lens1_base, model_path=save_path, z_lim=[32.5, 85], build_ipt_model=True, preview=False)
