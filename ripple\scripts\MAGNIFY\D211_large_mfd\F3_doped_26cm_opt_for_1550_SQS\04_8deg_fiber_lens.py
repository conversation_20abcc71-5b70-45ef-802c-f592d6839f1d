import numpy as np
import os
import matplotlib.pyplot as plt

from ripple.field import Trans<PERSON><PERSON>ield
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens, SingleSurface
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.geometry import rotate_vector
from ripple.utils.optics import cal_exit_angle
from ripple.utils.sim_helpers import run_sim


coupling_distance = 600
coupling_angle_deg = -8
c_wl = 1.55


# monitor_yz = FieldMonitor(monitor_type='yz', position=0, saving_path=None)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None, record_power_per_z=True,
                          record_beam_radius_per_z=True)

# lens1 = SingleSurface(  # Coupling efficiency: 0.9742942348486404
#             surface_type='ConoidalAsphericSurface',
#             surface_parameters={'x0': -22.166161057923937, 'y0': 0, 'z0': 260,
#                                 'rho_x': -0.011301823234413075, 'kappa_x': -0.566466337820632,
#                                 'rho_y': -0.011658508546386594, 'kappa_y': -0.43882292193578143},
#             max_radius=125/2, material_side=-1, refractive_index=1.53, priority=2)

lens1 = SingleSurface(  # Coupling efficiency: 0.9740976597825819
            surface_type='ConoidalAsphericSurface',
            surface_parameters={'x0': -22.2855859402148, 'y0': 0, 'z0': 260,
                                'rho_x': -0.011216055704743367, 'kappa_x': -0.5073008563446226,
                                'rho_y': -0.011698937259997575, 'kappa_y': -0.5009084681506905},
            max_radius=125/2, material_side=-1, refractive_index=1.53, priority=2)


source_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=10.4/2, w0y=10.4/2),
                               z0=0, refractive_index=1.53, wavelength=c_wl)
target_x_pos = (coupling_distance - lens1._surface_parameters['z0']) * np.sin(np.deg2rad(coupling_angle_deg))
print(f"{target_x_pos = }") # -35.34210958749277
target_field = TransverseField(distribution=GaussianDistribution(x0=target_x_pos,
                                                                 y0=0, w0x=15, w0y=15),
                               z0=coupling_distance, refractive_index=1, wavelength=c_wl)
target_field.rotate_field_xy(theta_y_deg=coupling_angle_deg)

lens1.translate(tx=0, ty=0, tz=0, attach_to=source_field, attach_after_translation=False)

scene = CouplingScene(input_field=source_field, target_field=target_field,
                      background_material_index=1.,
                      optical_structures=[lens1],
                      sim_size_xy=[200, 200],
                      # sim_z_end=target_center_z + 10,
                      wavelength_sampling_xy=4,
                      material_wavelength_sampling_z=4,
                      background_wavelength_sampling_z=2,
                      solver='wpm',
                      # solver='bvwpm', solver_pars={'polarization': 'x'},
                      monitors=[monitor_xz],
                      boundary_condition='PML', boundary_parameters={"boundary_thickness": [5]*2})

if __name__ == "__main__":
    if False:
        scene.preview(mode='xz', position=0)
        scene.preview(mode='yz', position=0)
        plt.show()

    if True:
        # current_dir = os.path.abspath(os.path.dirname(__file__))
        # save_dir = os.path.join(current_dir, 'gaussian_fit')
        save_dir = None
        opt_pars = {lens1.name: {
            'Surface': {
            # 'surface': ['x0'],
            'surface': ['x0', 'kappa_x', 'kappa_y', 'rho_x', 'rho_y'],
            },
            # 'Translation': ['x']
        }}


        run_sim(scene, optimize_structure=True, sim_result_dir=save_dir, opt_log_dir=save_dir,
                opt_pars=opt_pars, cal_coupling=True, show_plot=True)

    if False:
        plot_monitor_from_dir(dir_path=r'D:\WORK\02_Research\09_Hybrid_comb_packaging\03_Lens_design\04_QDMLLD_J5_to_SiN_B4_10cm\Design3_20deg\QDMLLD_lens_d3\forward', scene=scene)
        plt.show()

    if False:
        monitor_path = r'D:\WORK\02_Research\09_Hybrid_comb_packaging\03_Lens_design\04_QDMLLD_J5_to_SiN_B4_10cm\Design3_20deg\QDMLLD_SiN__lenses_d4\1_monitor_xy_2024-10-24_14-08-57.pkl'
        monitor = FieldMonitor.load(path=monitor_path)

        ce = monitor.cal_overlap_integral(input_field=source_field, field_type='E', operation='abs', direction='reflected', debug=True)
        print(f"{ce = }")   # 0.0028328535 (4, 4, 2)

