import numpy as np
from measurement.MFD_measurement.mfd_measurer import MFDMeas

# Pump current was set to 600 mA
SAVE_DIR = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\03_Fiber_D211_Fiber\00_1480nm_8deg_models\EDWA_lens\MFD\C9_doped_test_chip_with_1480nm_lens_1480nm_spiral2_OD1_right_side_angled2'

# '10x/0.25-VIS' pixel size: 1.5 um
# '100x/0.8-IR' pixel size: 0.15 um
mfd_meas = MFDMeas(stage_COM='COM3', initial_exposure=None, pixel_size_um=1.5, wavelength_um=1.48,
                   image_suffix='.tiff', camera_verbose=True,
                   max_saturated_pixels=1, target_pixel_brightness_percent=0.99,
                   mask_diameter=1.1, phi=0)

# # mfd_meas.initialize_stage()
# # mfd_meas.generate_equidistance_delta_z(z_step=-0.1, nr_pos=10)
mfd_meas.delta_z = np.array([-0.01] * 100 + [-0.02] * 50 + [-0.05] * 40)
#
# # mfd_meas.calibrate_beam_direction(max_range=-0.06)
mfd_meas.meas_mfd(save_dir=SAVE_DIR, show_each_plot=False, return_to_original_pos=True)

mfd_meas.fit_mfd(image_dir=SAVE_DIR, result_dir=SAVE_DIR, plot_each_fit=False, print_fit_info=False,
                 fit_simple_gaussian=False)
