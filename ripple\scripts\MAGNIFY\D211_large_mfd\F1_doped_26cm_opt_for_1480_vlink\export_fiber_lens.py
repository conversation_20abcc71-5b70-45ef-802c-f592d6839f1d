from ripple.utils.export_mesh import export_model
from wave_propagation_method.optical_elements2.basic_elements import Lens


save_path = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\03_Fiber_D211_Fiber\1480nm_8deg_models\fiber_lens\fiber_1480nm_8deg.obj'

abs_x_offset = 20.810597276975287    # only used for calculating the semidiameter
semi_diameter = 125/2
lens1_base = Lens('plane', [0],
                  'conoidal_xy_base', {'conoidal_coeff': {
                                                          'z0': 260,
                                'rho_x': -0.011624444618461089, 'kappa_x': -0.5164140095689257,
                                'rho_y': -0.011885383488811659, 'kappa_y': -0.5132562233437843
                                                          }},
                  semidiameter=(semi_diameter + abs_x_offset) * 1.2,
                  material_index=1.53,
                  element_name='L1')
export_model(yilin_lens_def=lens1_base, model_path=save_path, z_lim=[183.5, 261], sim_size=250, focal_dist=200,
             build_ipt_model=False, preview=True)