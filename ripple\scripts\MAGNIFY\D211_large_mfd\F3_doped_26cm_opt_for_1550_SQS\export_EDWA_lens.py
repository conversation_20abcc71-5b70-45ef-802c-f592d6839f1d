from ripple.utils.export_mesh import export_model
from wave_propagation_method.optical_elements2.basic_elements import Lens


save_path = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\03_Fiber_D211_Fiber\03_1550nm_7deg_SQS\EDWA_lens\D211_F3_1550nm_7deg.obj'

abs_x_offset = 6.13712316945949    # only used for calculating the semidiameter
semi_diameter = 125/2
lens1_base = Lens('plane', [0],
                  'conoidal_xy_base', {'conoidal_coeff': {
                                                          'z0': 87.61640911382281,
                                'rho_x': -0.03501265128187675, 'kappa_x': -0.6265310848024254,
                                'rho_y': -0.03510252344057418, 'kappa_y': -0.48286916053283746
                                                          # 'surf_offset_xy': (abs_x_offset, 0)
                                                          }},
                  semidiameter=(semi_diameter + abs_x_offset) * 1.2,
                  material_index=1.53,
                  element_name='L1')
export_model(yilin_lens_def=lens1_base, model_path=save_path, z_lim=[11, 88], build_ipt_model=True, preview=False)
