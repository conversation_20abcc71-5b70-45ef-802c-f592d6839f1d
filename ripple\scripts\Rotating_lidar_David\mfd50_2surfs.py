import os
import matplotlib.pyplot as plt
import numpy as np

from ripple.field import Transverse<PERSON>ield
from ripple.field_distribution import GaussianDistribution
from ripple.monitors import FieldMonitor
from ripple.structures import Lens
from ripple.coupling_optimizer import CouplingScene
from ripple.utils.sim_helpers import run_sim, plot_monitor_from_dir

focus_distance = 2000

source_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=10.4 / 2, w0y=10.4 / 2),
                               z0=0, refractive_index=1.53, wavelength=1.55)
target_field = TransverseField(distribution=GaussianDistribution(x0=0, y0=0, w0x=50 / 2, w0y=50 / 2),
                               z0=focus_distance, refractive_index=1,
                               wavelength=source_field.wavelength)
monitor_xz = FieldMonitor(monitor_type='xz', position=0, saving_path=None,
                          record_power_per_z=True, record_beam_radius_per_z=True)

lens = Lens(first_surface_type='ConoidalSurface',
            first_surface_parameters={'x0': 0, 'y0': 0, 'z0': 25.00913193517434,
                                      'rho': -0.0625362523752744, 'kappa': -0.25808018276385486},
            second_surface_type='ConoidalSurface',
            second_surface_parameters={'x0': 0, 'y0': 0, 'z0': 262.16082672734353,
                                       'rho': -0.01133680094904033, 'kappa': -0.4934564823731161},
            max_radius=75,
            refractive_index=1.53,
            priority=1)

scene = CouplingScene(input_field=source_field, target_field=target_field,
                      background_material_index=1.,
                      optical_structures=[lens],
                      sim_size_xy=[200, 200],
                      wavelength_sampling_xy=[4, 4],
                      material_wavelength_sampling_z=8,
                      background_wavelength_sampling_z=4,
                      monitors=[monitor_xz],
                      boundary_condition='ABC', boundary_parameters=None)


def run(save_dir=None):
    run_sim(scene, optimize_structure=False, opt_pars=None, opt_method='Nelder-Mead', cal_coupling=True,
            name='optimized', sim_result_dir=save_dir, opt_log_dir=None, show_plot=True)


def optimize(save_dir=None):
    opt_pars = {lens.name: {'Surface': {'first_surface': ['z0', 'kappa', 'rho'],
                                        'second_surface': ['z0', 'kappa', 'rho']}}}
    run_sim(scene, optimize_structure=True, opt_pars=opt_pars, opt_method='Nelder-Mead', cal_coupling=True,
            name='optimized', sim_result_dir=save_dir, opt_log_dir=save_dir, show_plot=True)


if __name__ == '__main__':
    # scene.preview(mode='xz', position=0)
    # plt.show()

    # save_dir = 'data'
    save_dir = None

    # optimize(save_dir=save_dir)
    run(save_dir=save_dir)

    # plot_monitor_from_dir(save_dir, scene=scene)
    # plt.show()
