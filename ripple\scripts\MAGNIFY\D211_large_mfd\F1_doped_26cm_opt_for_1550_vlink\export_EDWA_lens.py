from ripple.utils.export_mesh import export_model
from wave_propagation_method.optical_elements2.basic_elements import Lens


save_path = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\03_Fiber_D211_Fiber\01_1550nm_7deg_models\EDWA_lens\EDWA_1550nm_7deg.obj'

abs_x_offset = 6.372443970527434    # only used for calculating the semidiameter
semi_diameter = 125/2
lens1_base = Lens('plane', [0],
                  'conoidal_xy_base', {'conoidal_coeff': {
                                                          'z0': 80.45851925405488,
                                'rho_x': -0.0370327837869302, 'kappa_x': -0.6209702399511289,
                                'rho_y': -0.03658151285410804, 'kappa_y': -0.45757189015688143
                                                          # 'surf_offset_xy': (abs_x_offset, 0)
                                                          }},
                  semidiameter=(semi_diameter + abs_x_offset) * 1.2,
                  material_index=1.53,
                  element_name='L1')
export_model(yilin_lens_def=lens1_base, model_path=save_path, z_lim=[0, 81], build_ipt_model=True, preview=False)
