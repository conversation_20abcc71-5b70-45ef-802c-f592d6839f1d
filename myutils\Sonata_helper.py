import numpy as np
import math


def normal_to_euler_angles(normal_vector, rotation_order='XYZ'):
    """
    Convert a plane normal vector to Euler angles that would rotate
    the XY plane (normal [0,0,1]) to align with the given normal.

    Parameters:
    normal_vector: array-like, the normal vector of the target plane
    rotation_order: str, the order of rotations ('XYZ', 'ZYX', etc.)

    Returns:
    tuple: (rx, ry, rz) rotation angles in radians
    """
    # Normalize the input vector
    normal = np.array(normal_vector, dtype=float)
    normal = normal / np.linalg.norm(normal)

    # Original normal vector (XY plane)
    original_normal = np.array([0, 0, 1])

    # Method 1: Using rotation matrix approach
    # Find the rotation matrix that aligns [0,0,1] with the target normal

    # If vectors are already aligned
    if np.allclose(normal, original_normal):
        return (0, 0, 0)

    # If vectors are opposite
    if np.allclose(normal, -original_normal):
        return (math.pi, 0, 0)  # 180 degree rotation around X

    # Find rotation axis (cross product)
    rotation_axis = np.cross(original_normal, normal)
    rotation_axis = rotation_axis / np.linalg.norm(rotation_axis)

    # Find rotation angle
    cos_angle = np.dot(original_normal, normal)
    angle = math.acos(np.clip(cos_angle, -1, 1))

    # Create rotation matrix using Rodrigues' formula
    K = np.array([[0, -rotation_axis[2], rotation_axis[1]],
                  [rotation_axis[2], 0, -rotation_axis[0]],
                  [-rotation_axis[1], rotation_axis[0], 0]])

    R = np.eye(3) + math.sin(angle) * K + (1 - math.cos(angle)) * np.dot(K, K)

    # Extract Euler angles from rotation matrix
    if rotation_order == 'XYZ':
        return rotation_matrix_to_euler_xyz(R)
    elif rotation_order == 'ZYX':
        return rotation_matrix_to_euler_zyx(R)
    else:
        raise ValueError("Supported rotation orders: 'XYZ', 'ZYX'")


def rotation_matrix_to_euler_xyz(R):
    """Extract XYZ Euler angles from rotation matrix"""
    # XYZ order: R = Rz(γ) * Ry(β) * Rx(α)
    sy = math.sqrt(R[0, 0] ** 2 + R[1, 0] ** 2)

    singular = sy < 1e-6

    if not singular:
        x = math.atan2(R[2, 1], R[2, 2])
        y = math.atan2(-R[2, 0], sy)
        z = math.atan2(R[1, 0], R[0, 0])
    else:
        x = math.atan2(-R[1, 2], R[1, 1])
        y = math.atan2(-R[2, 0], sy)
        z = 0

    return (x, y, z)


def rotation_matrix_to_euler_zyx(R):
    """Extract ZYX Euler angles from rotation matrix"""
    # ZYX order: R = Rx(γ) * Ry(β) * Rz(α)
    sy = math.sqrt(R[0, 0] ** 2 + R[1, 0] ** 2)

    singular = sy < 1e-6

    if not singular:
        x = math.atan2(R[2, 1], R[2, 2])
        y = math.atan2(-R[2, 0], sy)
        z = math.atan2(R[1, 0], R[0, 0])
    else:
        x = math.atan2(-R[1, 2], R[1, 1])
        y = math.atan2(-R[2, 0], sy)
        z = 0

    return (z, y, x)  # Return in ZYX order


def apply_xyz_rotation(vector, rx, ry, rz):
    """Apply XYZ Euler rotations to a vector"""
    # Rotation matrices
    Rx = np.array([[1, 0, 0],
                   [0, math.cos(rx), -math.sin(rx)],
                   [0, math.sin(rx), math.cos(rx)]])

    Ry = np.array([[math.cos(ry), 0, math.sin(ry)],
                   [0, 1, 0],
                   [-math.sin(ry), 0, math.cos(ry)]])

    Rz = np.array([[math.cos(rz), -math.sin(rz), 0],
                   [math.sin(rz), math.cos(rz), 0],
                   [0, 0, 1]])

    # Apply rotations in XYZ order
    result = Rz @ Ry @ Rx @ vector
    return result


def degrees(radians):
    """Convert radians to degrees"""
    return math.degrees(radians)


def rough_angle_calculation(distance, z_offset):
    """Note that the z_offset from the stage position should be negative."""
    return np.rad2deg(np.arctan(z_offset / distance))


def main():
    # normal = [-0.00846262, 0.00774745, 0.999934]
    # normal = [-0.00820189, 0.00759432, 0.999938]
    normal = [0.000239347, 0.00256737, 0.999997]
    rot_order = 'XYZ'
    print(f"Normal vector {normal}")

    rx, ry, rz = normal_to_euler_angles(normal, rot_order)
    print(f"Order: {rot_order}, Rx={degrees(rx):.2f}°, Ry={degrees(ry):.2f}°, Rz={degrees(rz):.2f}°")

    # Test Method 1
    original = np.array([0, 0, 1])

    rotated = apply_xyz_rotation(original, rx, ry, rz)
    target_normal = np.array(normal) / np.linalg.norm(normal)
    error = np.linalg.norm(rotated - target_normal)
    print(f"Verification error: {error:.6f}")

    halved = apply_xyz_rotation(original, rx / 2, ry / 2, rz)
    print(f"Norm with halved angle: {halved}")

    ## 2025-08-02 FA3 printing log
    # ch1 print with fixed tilt but inversed x.y norm: [0.00820189, -0.00759432, 0.999938]
    # The rest of the channels printed with the same fixed normal: [-0.00820189, 0.00759432, 0.999938]


# Example usage and testing
if __name__ == "__main__":
    theta = rough_angle_calculation(distance=250*15, z_offset=6692.10 - 6702.20)
    print(f"Rough angle: {theta:.3f}°")
    # main()
