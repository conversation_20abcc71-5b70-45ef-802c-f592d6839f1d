import traceback
import time
import os
import glob
from tqdm import tqdm
from datetime import datetime
import numpy as np
import pandas as pd
from scipy.signal import butter, filtfilt
import matplotlib.pyplot as plt
from functools import partial

from device_wrappers.Newfocus_TLB6700 import NewFocus_TLB6700_wrapper
from device_wrappers.Thorlabs_PMxxx.PMxxx_wrapper import PMxxx

def butter_lowpass(cutoff, fs, order=5):
    nyq = 0.5 * fs
    normal_cutoff = cutoff / nyq
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    return b, a

def butter_lowpass_filter(data, cutoff, fs, order=5):
    b, a = butter_lowpass(cutoff, fs, order)
    y = filtfilt(b, a, data)  # <- zero-phase filter
    return y

def butter_highpass(cutoff, fs, order=3):
    nyq = 0.5 * fs
    normal_cutoff = cutoff / nyq
    return butter(order, normal_cutoff, btype='high', analog=False)

def butter_highpass_filter(data, cutoff, fs, order=3):
    b, a = butter_highpass(cutoff, fs, order)
    return filtfilt(b, a, data)

# --- Preprocessing ---
def moving_average(x, w):
    return np.convolve(x, np.ones(w), 'valid') / w

def normalization(x):
    return x / np.mean(x)

def dBm_to_mW(opt_power):
    watt = (10.0**(opt_power/10.0))
    return watt


class ReflectivityCharacterization:
    def __init__(self):
        self.center_wl_nm = 1550
        self.laser_power = 3

        self.ecl = None
        self.pd = None

    def init(self):
        self.ecl = NewFocus_TLB6700_wrapper.NewFocus6700(id=4106, key='6700 SN70006')
        self.ecl.connect_laser(True)
        self.init_ecl()

        self.pd = PMxxx()
        self.init_pd()

    def init_ecl(self):
        # self.ecl.reset()
        self.ecl.set_lbd(Lambda=self.center_wl_nm)
        self.ecl.wait_for_tracking()
        self.ecl.set_pzt_voltage(0)
        self.ecl.set_constant_power_mode()
        self.ecl.set_out_power(power_mW=self.laser_power)

    def init_pd(self):
        self.pd.wavelength = self.center_wl_nm
        self.pd.auto_range = False
        self.pd.set_power_range(value_W=3.9e-3)  # TODO: check the power range
        self.pd.set_average(3)

    # def cal_scan_time(self):
    #     # wl_lims  = self.ecl.query_wl_scan_limit()
    #     # print(f"Wavelength limits: {wl_lims}")    # empty
    #     wl_span = self.end_wl - self.start_wl
    #     # frd_speed, bwd_speed = self.ecl.query_scan_speed()    # empty
    #     # scan_config = self.ecl.get_scan_config()
    #     # print(f"Scan config: {scan_config}")
    #     scan_time = wl_span / self.fwd_scan_speed
    #     if self.bwd_scan_speed != 0:
    #         scan_time += wl_span / self.bwd_scan_speed
    #
    #     return scan_time

    # def start_sweeping(self):
    #     scan_time = self.cal_scan_time()
    #     print(f"Scan time: {scan_time}")
    #
    #     self.ecl.set_output_state(value=True)
    #     self.ecl.start_wl_scan_process(True)  # the scan always go forth and back. Back scan is faster than forward.
    #     time.sleep(scan_time + 3)

    # def measure_pt_by_pt(self):
    #     self.ecl.set_output_state(value=True)
    #     wls = np.linspace(self.start_wl, self.end_wl, 10)
    #     pws = np.zeros_like(wls)
    #     for i, wl in enumerate(tqdm(wls, desc="Sweeping wavelengths", unit="wavelengths")):
    #         self.ecl.set_lbd(Lambda=wl)
    #         self.pd.wavelength = wl
    #         self.ecl.wait_for_tracking()
    #         # while not self.ecl.is_operation_complete():
    #         # time.sleep(0.1)
    #         pws[i] = self.pd.get_power()
    #     return wls, pws

    def measure_pt_by_pt_piezo(self, iterations=3, nr_pt_per_iter=400):
        if self.ecl is None or self.pd is None:
            self.init()
        self.ecl.set_output_state(value=True)
        self.ecl.set_track_mode(False)

        piezo_pcts = np.linspace(0, 100, nr_pt_per_iter)
        non_overlap_piezo_pcts = np.concatenate([piezo_pcts + iteration * 100 for iteration in range(iterations)])
        pws = np.zeros_like(non_overlap_piezo_pcts)
        for iteration in range(iterations):
            for i, piezo_pct in enumerate(tqdm(piezo_pcts, desc="Sweeping with piezo")):
                self.ecl.set_pzt_voltage(piezo_pct)
                time.sleep(0.2)
                pws[i + iteration * nr_pt_per_iter] = self.pd.get_power()
                time.sleep(0.05)
        time.sleep(1)
        self.ecl.set_pzt_voltage(0)
        return non_overlap_piezo_pcts, pws

    def save_data_to_csv(self, piezo_pcts, powers, filepath=None):
        # Filter out NaN values and invalid readings
        valid_mask = ~(np.isnan(piezo_pcts) | np.isnan(powers))

        if not np.any(valid_mask):
            print("Warning: No valid data points to save!")
            return None

        data = {
            'piezo_percentage': piezo_pcts[valid_mask],
            'power_dBm': powers[valid_mask]
        }

        df = pd.DataFrame(data)

        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f'reflectivity_data_{timestamp}.csv'

        # Ensure filepath has .csv extension
        if not filepath.endswith('.csv'):
            filepath += '.csv'

        os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

        df.to_csv(filepath, index=False)
        print(f"Data saved to {filepath} ({len(df)} valid data points)")
        return filepath

    @staticmethod
    def read_data_from_csv(filename):
        if not os.path.exists(filename):
            raise FileNotFoundError(f"File {filename} not found")

        df = pd.read_csv(filename)
        required_columns = ['piezo_percentage', 'power_dBm']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        piezo_pcts = df['piezo_percentage'].values
        powers = df['power_dBm'].values

        print(f"Loaded {len(df)} data points from {filename}")
        return piezo_pcts, powers

    def close(self):
        if self.ecl is not None:
            self.ecl.set_output_state(value=False)
        if self.pd is not None:
            self.pd.close()

    def extract_reflectivity_with_reference(self, filepath, reference, show_plot=True, save_plot=True, nr_iterations_to_use=None):
        """
        STEP 1: normalized the data
        STEP 2: subtract the reference
        STEP 3: extract the fringe contrast
        """
        # Load data
        assert filepath.endswith('.csv'), 'Measurement data should be a csv file.'
        piezo_pcts, transmission_dBm = self.read_data_from_csv(filepath)

        ref_piezo_pcts, ref_transmission_dBm = self.read_data_from_csv(reference)
        assert np.all(piezo_pcts == ref_piezo_pcts), f'The piezo percentages from the reference are different from the ones under test.\n{piezo_pcts = }\n{ref_piezo_pcts = }'

        if nr_iterations_to_use is not None:
            piezo_pcts, transmission_dBm = self.cut_data(piezo_pcts, transmission_dBm, nr_iters=nr_iterations_to_use)
            ref_piezo_pcts, ref_transmission_dBm = self.cut_data(ref_piezo_pcts, ref_transmission_dBm, nr_iters=nr_iterations_to_use)

        norm_trans = normalization(dBm_to_mW(transmission_dBm))
        norm_ref_trans = normalization(dBm_to_mW(ref_transmission_dBm))

        data = norm_trans - norm_ref_trans + np.mean(norm_trans)

        Imin, Imax, K, R = self.calculate_R(data, start_idx=0, end_idx=len(data))

        plt.figure(figsize=(10, 5))
        plt.plot(piezo_pcts, data, label='Normalized and calibrated')

        plt.axhline(Imax, color='r', linestyle='--', label='$T_{max}$')
        plt.axhline(Imin, color='purple', linestyle='--', label='$T_{min}$')
        plt.ylim([0.0, 1.2])
        plt.xlabel('Piezo voltage (V)')
        plt.ylabel('Normalized transmission (a.u.)')
        plt.title(f'Fringe contrast = {K:.4f}, Reflectivity = {R * 100:.2f}%')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        if save_plot:
            plt.savefig(filepath[:-len('.csv')] + '_calibrated.png', dpi=400, transparent=False)
        if show_plot:
            plt.show()
        plt.close()

    @staticmethod
    def calculate_R(data, start_idx, end_idx):
        """start_idx and end_idx are used to cut piece of data that are useful for fitting."""
        Imax = np.max(data[start_idx:end_idx])
        Imin = np.min(data[start_idx:end_idx])
        K = (Imax - Imin) / (Imax + Imin)
        R = (1 - np.sqrt(1 - K ** 2)) / K
        print(f"Fringe contrast K = {K:.4f}")
        print(f"Estimated facet reflectivity R = {R * 100:.2f}%")
        return Imin, Imax, K, R

    @staticmethod
    def cut_data(piezo_pcts, transmission, nr_iters):
        if piezo_pcts[-1] / 100 < nr_iters:
            print(f"The dataset only contains {int(piezo_pcts[-1] / 100)} iterations.")
            return piezo_pcts, transmission
        one_iter_end = np.where(piezo_pcts == nr_iters * 100)[0][0]
        return piezo_pcts[:one_iter_end + 1], transmission[:one_iter_end + 1]
            

    def extract_reflectivity(self, filepath, avg_window_size=5, lowpass_cutoff=0.15, highpass_cutoff=0.012,
                             norm_fitting_range=None, show_plot=True, save_plot=True, nr_iterations_to_use=None):
        """
        STEP 1: Smooth the data by averaging
        STEP 2: normalize the data
        STEP 3: Low-pass filter the data
        STEP 4: Remove the baseline
        """
        assert filepath.endswith('.csv'), 'Measurement data should be a csv file.'
        piezo_pcts, transmission_dBm = self.read_data_from_csv(filepath)

        if nr_iterations_to_use is not None:
            piezo_pcts, transmission_dBm = self.cut_data(piezo_pcts, transmission_dBm, nr_iters=nr_iterations_to_use)

        transmission = dBm_to_mW(transmission_dBm)

        norm_trans = normalization(transmission)
        norm_smooth_trans = normalization(moving_average(transmission, avg_window_size))

        fs_v = 1.0 / (piezo_pcts[1] - piezo_pcts[0])
        # Debug: check the Fourier spectrum of the raw data
        # from scipy.fft import fft, fftfreq
        # N = len(transmission)
        # T = 1/fs_v
        # yf = fft(transmission)
        # xf = fftfreq(N, T)[:N//2]
        #
        # plt.figure(figsize=(10, 5))
        # plt.plot(xf, 2.0/N * np.abs(yf[:N//2]), label='Spectrum of raw data')
        # plt.show()
        
        # Cut off high frequency noise
        lowpassed_data = butter_lowpass_filter(norm_smooth_trans, lowpass_cutoff, fs_v, order=3)
        # Cut off low frequency noise that causes the slow drift
        highpassed_data = butter_highpass_filter(lowpassed_data, highpass_cutoff, fs_v)
        data = highpassed_data + np.mean(norm_smooth_trans)

        # --- Extract fringe contrast ---
        # Define a region where FP fringes are visible and stable
        if norm_fitting_range is None:
            norm_fitting_range = [0., 1.]
        start_idx = int(len(piezo_pcts) * norm_fitting_range[0])
        end_idx = int(len(piezo_pcts) * norm_fitting_range[1])
        Imin, Imax, K, R = self.calculate_R(data, start_idx=start_idx, end_idx=end_idx)

        plt.figure(figsize=(10, 5))
        plt.plot(piezo_pcts, norm_trans, label='normalized')
        # plt.plot(piezo_pcts[avg_window_size - 2:-1], norm_smooth_trans, label='normalized, smoothed')
        plt.plot(piezo_pcts[avg_window_size - 2:-1], data, label='Filtered')
        plt.plot(piezo_pcts[avg_window_size - 2:-1][start_idx:end_idx], data[start_idx:end_idx], label='For extracting R')

        plt.axhline(Imax, color='r', linestyle='--', label='$T_{max}$')
        plt.axhline(Imin, color='purple', linestyle='--', label='$T_{min}$')
        plt.ylim([0.0, 1.2])
        plt.xlabel('Piezo voltage (V)')
        plt.ylabel('Normalized transmission (a.u.)')
        plt.title(f'Fringe contrast = {K:.4f}, Reflectivity = {R * 100:.2f}%')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        if save_plot:
            plt.savefig(filepath[:-len('.csv')] + '.png', dpi=400, transparent=False)
        if show_plot:
            plt.show()
        plt.close()


def plot_folder(folder_path):
    csv_files = glob.glob(os.path.join(folder_path, '*.csv'), recursive=False)
    for csv_file in csv_files:
        plot_measurement(csv_file, save_plot=True, show_plot=False)


def fit_folder(folder_path, method):
    csv_files = glob.glob(os.path.join(folder_path, '*.csv'), recursive=False)
    for csv_file in csv_files:
        method(filepath=csv_file)


def plot_measurement(csv_file, save_plot=False, show_plot=True):
    piezo_pcts, powers, _ = ReflectivityCharacterization.read_data_from_csv(csv_file)

    fig, ax = plt.subplots(figsize=(6, 4))

    # Subplot 2: Power vs Piezo Percentage (only if piezo data is available)
    ax.plot(piezo_pcts, powers, '.-', markersize=3)
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('Piezo Percentage (%)')
    ax.set_ylabel('Power (dBm)')
    ax.set_title(f' Power vs Piezo Percentage')

    plt.tight_layout()
    if save_plot:
        plt.savefig(csv_file[:-len('.csv')] + '.png', dpi=300, bbox_inches='tight')
    if show_plot:
        plt.show()
    plt.close()


if __name__ == '__main__':
    save_dir = r'C:\Users\<USER>\Documents\EDWA_setup\2025-08-04_reflection_measurement_D211_1480nmLens_loopback'
    measurement_name = 'D211_loopback_1480nm_lens.csv'
    save_path = os.path.join(save_dir, measurement_name)

    reflectivity_char = ReflectivityCharacterization()
    # try:
    #     piezo_p, pws = reflectivity_char.measure_pt_by_pt_piezo(iterations=3, nr_pt_per_iter=400)
    #     csv_filename = reflectivity_char.save_data_to_csv(piezo_p, pws, filepath=save_path)
    # except:
    #     print(traceback.format_exc())
    # finally:
    #     reflectivity_char.close()

    # plot_measurement(save_path, save_plot=False, show_plot=True)
    
    fitting_method = partial(reflectivity_char.extract_reflectivity, avg_window_size=10, lowpass_cutoff=0.1, highpass_cutoff=0.012,
                                                                     show_plot=True, save_plot=True, norm_fitting_range=[0., 1.], nr_iterations_to_use=None)
    
    # fitting_method = partial(reflectivity_char.extract_reflectivity_with_reference, 
    #                          filepath=save_path, reference=os.path.join(r'C:\Users\<USER>\Documents\EDWA_setup\2025-08-08_reflection_measurement_D211_F3_1550nmLens_SQSfiber_loopback1', 'Fiber_reference.csv'), 
    #                          show_plot=True, save_plot=True, nr_iterations_to_use=None)

    fit_folder(save_dir, fitting_method)
    