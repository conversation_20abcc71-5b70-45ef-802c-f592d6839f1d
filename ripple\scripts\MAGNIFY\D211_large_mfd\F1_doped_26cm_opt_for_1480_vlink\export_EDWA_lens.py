from ripple.utils.export_mesh import export_model
from wave_propagation_method.optical_elements2.basic_elements import Lens


save_path = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\03_Fiber_D211_Fiber\1480nm_8deg_models\EDWA_lens\EDWA_1480nm_8deg.obj'

abs_x_offset = 7.197662004592751    # only used for calculating the semidiameter
semi_diameter = 125/2
lens1_base = Lens('plane', [0],
                  'conoidal_xy_base', {'conoidal_coeff': {
                                                          'z0': 88.14296514005312,
                                'rho_x': -0.03365178596101758, 'kappa_x': -0.6548162772254343,
                                'rho_y': -0.03323723229458316, 'kappa_y': -0.47542193345460226
                                                          # 'surf_offset_xy': (abs_x_offset, 0)
                                                          }},
                  semidiameter=(semi_diameter + abs_x_offset) * 1.2,
                  material_index=1.53,
                  element_name='L1')
export_model(yilin_lens_def=lens1_base, model_path=save_path, z_lim=[0, 90], build_ipt_model=False, preview=True)

z_min = input('Z min: ')
z_max = input('Z max: ')
export_model(yilin_lens_def=lens1_base, model_path=save_path, z_lim=[z_min, z_max], build_ipt_model=True, preview=False)