import os
import pandas as pd
import matplotlib.pyplot as plt


# Channel center frequencies in THz (from ch1 to ch14)
freq_centers = [194.46418, 194.56364, 194.6631 , 194.76256, 194.86202, 194.96148, 195.06094, 195.1604 ,
                195.25986, 195.35932, 195.45878, 195.55824, 195.6577 , 195.75716]

def load_ber_data(filepath):
    """Load BER data from the log file."""
    try:
        data = pd.read_csv(filepath)
        return data
    except FileNotFoundError:
        print(f"Error: File {filepath} not found.")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def plot_ber_over_frequency_multiple(data_list, labels, freq_centers):
    """Plot BER vs channel frequency for multiple datasets."""
    if not data_list or all(data is None for data in data_list):
        print("No valid data to plot.")
        return

    # Create the plot
    plt.figure(figsize=(8, 6))

    # Define colors and markers for different datasets
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']
    markers = ['o', 's', '^', 'D', 'v', '<']

    # Plot BER vs frequency
    plt.subplot(1, 1, 1)

    for i, (data, label) in enumerate(zip(data_list, labels)):
        if data is None:
            continue

        # Ensure we have the right number of frequency points
        current_freq = freq_centers.copy()
        current_data = data.copy()

        if len(current_freq) != len(current_data):
            print(f"Warning: Number of frequency centers ({len(current_freq)}) doesn't match data points ({len(current_data)}) for {label}")
            # Use only the available data points
            min_len = min(len(current_freq), len(current_data))
            current_freq = current_freq[:min_len]
            current_data = current_data.iloc[:min_len]

        # Plot with different colors and markers
        color = colors[i % len(colors)]
        marker = markers[i % len(markers)]
        plt.semilogy(current_freq, current_data['BER'],
                    color=color, marker=marker, linestyle='-',
                    linewidth=2, markersize=8, label=label)

    # Add SD(soft-decision)-FEC limit line
    sdfec_limit = 2e-2
    plt.axhline(y=sdfec_limit, color='red', linestyle='--', linewidth=2,
                label='SD-FEC Limit (2×10⁻²)', alpha=0.8)
    # Add HD(hard-decision)-FEC limit line
    hdfec_limit = 3.8e-3
    plt.axhline(y=hdfec_limit, color='gray', linestyle='--', linewidth=2,
                label=r'HD-FEC Limit (3.8×10$^{-3}$)', alpha=0.8)

    plt.xlabel('Frequency (THz)')
    plt.ylabel('Bit Error Ratio (BER)')
    plt.title('BER vs Channel Frequency - Comparison')
    plt.grid(True, alpha=0.3)
    plt.legend()

    # Add channel labels (only for the first dataset to avoid clutter)
    if data_list[0] is not None:
        current_freq = freq_centers.copy()
        current_data = data_list[0].copy()
        if len(current_freq) != len(current_data):
            min_len = min(len(current_freq), len(current_data))
            current_freq = current_freq[:min_len]
            current_data = current_data.iloc[:min_len]

        for j, freq in enumerate(current_freq):
            plt.annotate(f'CH{j+1}', (freq, current_data['BER'].iloc[j]),
                        textcoords="offset points", xytext=(0,15),
                        ha='center', fontsize=8, alpha=0.7)

    # # Plot SNR vs frequency for reference
    # plt.subplot(2, 1, 2)
    #
    # for i, (data, label) in enumerate(zip(data_list, labels)):
    #     if data is None:
    #         continue
    #
    #     # Ensure we have the right number of frequency points
    #     current_freq = freq_centers.copy()
    #     current_data = data.copy()
    #
    #     if len(current_freq) != len(current_data):
    #         min_len = min(len(current_freq), len(current_data))
    #         current_freq = current_freq[:min_len]
    #         current_data = current_data.iloc[:min_len]
    #
    #     # Plot with different colors and markers
    #     color = colors[i % len(colors)]
    #     marker = markers[i % len(markers)]
    #     plt.plot(current_freq, current_data['SNR'],
    #             color=color, marker=marker, linestyle='-',
    #             linewidth=2, markersize=8, label=label)
    #
    # plt.xlabel('Frequency (THz)')
    # plt.ylabel('SNR (dB)')
    # plt.title('SNR vs Channel Frequency - Comparison')
    # plt.grid(True, alpha=0.3)
    # plt.legend()

    # Add channel labels (only for the first dataset to avoid clutter)
    if data_list[0] is not None:
        current_freq = freq_centers.copy()
        current_data = data_list[0].copy()
        if len(current_freq) != len(current_data):
            min_len = min(len(current_freq), len(current_data))
            current_freq = current_freq[:min_len]
            current_data = current_data.iloc[:min_len]

        for j, freq in enumerate(current_freq):
            plt.annotate(f'CH{j+1}', (freq, current_data['SNR'].iloc[j]),
                        textcoords="offset points", xytext=(0,10),
                        ha='center', fontsize=8, alpha=0.7)

    plt.tight_layout()
    plt.savefig(os.path.join(filedir, 'BER_vs_frequency_comparison.png'), dpi=400, transparent=True)
    plt.show()

def plot_ber_over_frequency(data, freq_centers):
    """Plot BER vs channel frequency for single dataset (kept for backward compatibility)."""
    if data is None:
        return
    plot_ber_over_frequency_multiple([data], ['Single Dataset'], freq_centers)

def print_summary_statistics(data, freq_centers):
    """Print summary statistics of the BER measurements."""
    if data is None:
        return

    print("\n=== BER Measurement Summary ===")
    print(f"Number of channels: {len(data)}")
    print(f"Frequency range: {min(freq_centers):.3f} - {max(freq_centers):.3f} THz")
    print(f"Average BER: {data['BER'].mean():.2e}")
    print(f"Best BER: {data['BER'].min():.2e} (Channel {data.loc[data['BER'].idxmin(), 'CH']})")
    print(f"Worst BER: {data['BER'].max():.2e} (Channel {data.loc[data['BER'].idxmax(), 'CH']})")
    print(f"Average SNR: {data['SNR'].mean():.2f} dB")
    print(f"Best SNR: {data['SNR'].max():.2f} dB (Channel {data.loc[data['SNR'].idxmax(), 'CH']})")
    print(f"Worst SNR: {data['SNR'].min():.2f} dB (Channel {data.loc[data['SNR'].idxmin(), 'CH']})")

if __name__ == "__main__":
    filedir = r'R:\Peng\QDMLLD_WDM\Demodulation results'
    filenames = [r'16QAM_demod_OB2B_log.csv', r'16QAM_demod_87km_log.csv', r'32QAM_demod_OB2B_log.csv']

    # Create descriptive labels for each dataset
    labels = ['16QAM OB2B', '16QAM 87km', '32QAM OB2B']

    # Load all datasets
    data_list = []
    valid_labels = []

    for filename, label in zip(filenames, labels):
        filepath = os.path.join(filedir, filename)
        data = load_ber_data(filepath)
        if data is not None:
            data_list.append(data)
            valid_labels.append(label)
            print(f"Successfully loaded: {label}")
            # Print summary statistics for each dataset
            print(f"\n=== {label} Summary ===")
            print_summary_statistics(data, freq_centers)
        else:
            print(f"Failed to load: {label} ({filename})")

    # Plot all valid datasets together
    if data_list:
        print(f"\nPlotting {len(data_list)} datasets together...")
        plot_ber_over_frequency_multiple(data_list, valid_labels, freq_centers)
    else:
        print("No valid data files found. Please check the file paths and formats.")
