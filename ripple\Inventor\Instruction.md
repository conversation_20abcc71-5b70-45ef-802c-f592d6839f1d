## Model building steps
### Step 1
Export using the python script. This should export lens surface, repair the model and build the .ipt file.

e.g., ripple\scripts\MAGNIFY\D174_01\export_mesh.py

### Step 2
Open the .ipt file and convert the object to base feature and select solid. Afterwards delete the original structure.

### Step 3
Measure the x_r, y_r, and z_bottom of the model in inventor. Update those values in the inventor python script.
If there is rotation, etc., put the parameters from the simulation script to the inventor script.

e.g., ripple/Inventor/build_straight_lens_side_print.py

### Step 4
Create a new inventor assembly. Import the .ipt file and align the lens axis with the z axis of the global coordinates.

### Step 5
Export the lens as .obj or .stl file with the highest resolution (in "Options" during export). For stl file, choose 'binary' format, instead of ASCII. 
Binary format results in a much smaller file size with the same number of triangles.
Note: you might need to repair (remove T vertices) the exported stl file again with meshlab!

### *Step 6
If the mesh is too large, import the .stl file to Meshmixer and simplify the mesh if necessary. 
- `Control + a` to select all
- Edit - reduce, use default settings.

### TODO
[] Automate those steps.

## Programming
Available objects:
[Inventor API object model](https://damassets.autodesk.net/content/dam/autodesk/www/pdfs/Inventor2022ObjectModel.pdf)

Available function for the object in the search window (top right) of inventor app. For example, I want to know how to what functions are available for "WorkAxes" object, then I search "WorkAxes" in the search window. The results are as follows:
https://help.autodesk.com/view/INVNTOR/2025/ENU/?guid=GUID-WorkAxes
