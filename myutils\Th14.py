from influxdb_client import InfluxDBClient
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# Connection Data
url = "http://*************:8086"
token = "4SPXQ9_jeeobEkc3hswm2Kdd93C9a3fmchDRIqePg8IzMNem7AQhoo7BCiPFht4cDszS5mPrcjMrxcL5YfPf3g=="
org = "IPQ"
bucket = "IPQ_SensorData"


start_time = '2025-08-07T00:00:00Z'
stop_time = '2025-08-12T00:00:00Z'

# Flux Query
query = f'''
from(bucket: "{bucket}")
  |> range(start: {start_time}, stop: {stop_time})
  |> filter(fn: (r) => r["device_name"] == "IPQ_TH14")
  |> filter(fn: (r) => r["_field"] == "value")
  |> filter(fn: (r) => r["SensorTyp"] == "Temperature Humidity Sensor")
  |> filter(fn: (r) => r["application_name"] == "Klimasensoren")
  |> filter(fn: (r) => r["_measurement"] == "device_frmpayload_data_TempC_SHT")
  |> aggregateWindow(every: 1m, fn: mean, createEmpty: false)
  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
  |> yield(name: "mean")
'''
# |> filter(fn: (r) => r["_measurement"] == "device_frmpayload_data_Hum_SHT")

# InfluxDB Client
client = InfluxDBClient(url=url, token=token, org=org)
query_api = client.query_api()

# Query 
df = query_api.query_data_frame(query=query, org=org)

# CSV exportieren
df.to_csv("export.csv", index=False)

print(" Export worked! Saved as 'export.csv'")


# Convert _time to datetime and set as index
df['_time'] = pd.to_datetime(df['_time'])
df = df.set_index('_time')

# Plot temperature vs datetime
plt.figure(figsize=(12, 6))
plt.plot(df.index, df['value'], 'b-', linewidth=1)
plt.xlabel('Date Time')
plt.ylabel('Temperature (°C)')
plt.title('Temperature Data from IPQ_TH14 Sensor')
plt.grid(True, alpha=0.3)

# Format x-axis to show dates nicely
plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=2))
plt.xticks(rotation=45)

plt.tight_layout()
plt.show()