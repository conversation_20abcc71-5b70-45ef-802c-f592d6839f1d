from influxdb_client import InfluxDBClient
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# Connection Data
url = "http://*************:8086"
token = "4SPXQ9_jeeobEkc3hswm2Kdd93C9a3fmchDRIqePg8IzMNem7AQhoo7BCiPFht4cDszS5mPrcjMrxcL5YfPf3g=="
org = "IPQ"
bucket = "IPQ_SensorData"

start_datetime = '2025-08-07T00:00:00Z'
stop_datetime = '2025-08-12T13:00:00Z'

# Flux Query
query = f'''
from(bucket: "{bucket}")
  |> range(start: {start_datetime}, stop: {stop_datetime})
  |> filter(fn: (r) => r["device_name"] == "IPQ_TH14")
  |> filter(fn: (r) => r["_field"] == "value")
  |> filter(fn: (r) => r["SensorTyp"] == "Temperature Humidity Sensor")
  |> filter(fn: (r) => r["application_name"] == "Klimasensoren")
  |> filter(fn: (r) => r["_measurement"] == "device_frmpayload_data_TempC_SHT")
  |> aggregateWindow(every: 1m, fn: mean, createEmpty: false)
  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
  |> yield(name: "mean")
'''
# |> filter(fn: (r) => r["_measurement"] == "device_frmpayload_data_Hum_SHT")

# InfluxDB Client
client = InfluxDBClient(url=url, token=token, org=org)
query_api = client.query_api()

# Query 
df = query_api.query_data_frame(query=query, org=org)

# df.to_csv("export.csv", index=False)

# print(" Export worked! Saved as 'export.csv'")


# Convert _time to datetime and set as index
df['_time'] = pd.to_datetime(df['_time'])
df = df.set_index('_time')

# Plot temperature vs datetime
plt.figure(figsize=(12, 6))
plt.plot(df.index, df['value'], 'b-', linewidth=1)
plt.ylabel('Temperature (°C)', fontsize=12)
plt.title('Temperature from IPQ_TH14 Sensor')
plt.grid(True, alpha=0.3)

# Add semi-transparent day overlays with alternating colors
ax = plt.gca()
y_min, y_max = ax.get_ylim()

# Define alternating colors (light blue and light gray with 30% opacity)
color = 'lightgray'
alphas = [0.1, 0.3]

# Get the date range from the data
start_date = df.index.min().normalize()  # Start of first day
end_date = df.index.max().normalize() + pd.Timedelta(days=1)  # Start of day after last day

# Create day boundaries
current_date = start_date
day_index = 0

while current_date < end_date:
    next_date = current_date + pd.Timedelta(days=1)

    alpha = alphas[day_index % 2]

    # Add rectangular overlay for the current day
    ax.axvspan(current_date, next_date,
               color=color, alpha=alpha, zorder=0)

    current_date = next_date
    day_index += 1

# Format x-axis with time labels and grouped date labels
ax = plt.gca()

# Set up time labels (HH:MM) on the main x-axis
ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))

ax.set_xlim([start_date, end_date])

# Create a secondary x-axis for date labels
ax2 = ax.twiny()
ax2.set_xlim(ax.get_xlim())

# Set up date labels to appear once per day
ax2.xaxis.set_major_locator(mdates.DayLocator())
ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))

# Position the date labels below the time labels
ax2.xaxis.set_ticks_position('bottom')
ax2.xaxis.set_label_position('bottom')

# Adjust the spacing between time and date labels (increased for rotated labels)
ax2.tick_params(axis='x', which='major', pad=40)

# Remove tick marks for date labels to avoid visual clutter
ax2.tick_params(axis='x', which='major', length=0)

# Rotate time labels to prevent overlap, keep date labels horizontal
plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
plt.setp(ax2.xaxis.get_majorticklabels(), rotation=0, ha='center')

# Adjust font size for better readability
ax.tick_params(axis='x', which='major', labelsize=9)
ax2.tick_params(axis='x', which='major', labelsize=10)

# Add x-axis label with increased padding to avoid overlap with tick labels
ax.set_xlabel('Date Time', labelpad=20, fontsize=12)

# Adjust spacing to accommodate rotated labels, date labels, and x-axis label
plt.subplots_adjust(bottom=0.35)

plt.tight_layout()
plt.show()