import numpy as np
from measurement.MFD_measurement.mfd_measurer import MFDMeas


SAVE_DIR = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\03_Fiber_D211_Fiber\vlink_16ch_pitch127um_fiberarray_mfd_w_lens\CH7_OD1_1550nm_10xObjective_fixed_distance'

# '10x/0.25-VIS' pixel size: 1.5 um
# '100x/0.8-IR' pixel size: 0.15 um
mfd_meas = MFDMeas(stage_COM='COM3', initial_exposure=None, pixel_size_um=1.5, wavelength_um=1.55,
                   image_suffix='.tiff', camera_verbose=True,
                   max_saturated_pixels=1, target_pixel_brightness_percent=0.99,
                   mask_diameter=3, phi=0)
# mfd_meas.initialize_stage()

# print(f"{np.array([-0.01] * 100 + [-0.02] * 50 + [-0.05] * 40) = }")
# mfd_meas.delta_z = np.array([-0.01] * 100 + [-0.02] * 50 + [-0.05] * 40)
#
# # mfd_meas.calibrate_beam_direction(max_range=-0.06)
# mfd_meas.meas_mfd(save_dir=SAVE_DIR, show_each_plot=False, return_to_original_pos=True)
# # mfd_meas.fit_mfd(image_dir=SAVE_DIR, result_dir=SAVE_DIR, plot_each_fit=False, print_fit_info=False,
# #                  fit_simple_gaussian=True)
mfd_meas.fit_mfd(image_dir=SAVE_DIR, result_dir=SAVE_DIR, plot_each_fit=False, print_fit_info=False,
                 fit_simple_gaussian=False)
